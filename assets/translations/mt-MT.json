{"FreezeAmount": "Freeze Amount", "OR": "OR", "aDayAgo": "a day ago", "aMinuteAgo": "a minute ago", "aMonthAgo": "a month ago", "aadhaar": "<PERSON><PERSON><PERSON><PERSON>", "aadhaarCardUnder": "ID/Passport/DL is under verification", "aboutUs": "About Us", "accountInformation": "Account Information", "accountInformationStatus1": "<PERSON><PERSON><PERSON><PERSON> card updated", "accountInformationStatus2": "Wallet password set successfully", "accountInformationStatus3": "Phone number registered successfully", "accountInformationStatus4": "Google code generated successfully", "accountName": "Account name", "accountNumber": "Account Number", "accountTotal": "Account Total", "actualAmount": "Actual Amount:", "actualProfit": "Actual Profit", "adAdminWill": "An admin will verify your information after that you can start using the platform", "addAccountInfo": "Complete your account information", "addAddressWarning": "Only support 6 withdrawal addresses and can not be changed.", "addWithdrawal": "Add <PERSON>drawal Address +", "addWithdrawalAddress": "<PERSON><PERSON> Address", "added": "Added", "additionalAmount": "Additional amount", "additionalFunds": "Additional funds", "additionalInvestment": "Additional Investment", "address": "Address", "addressNameTextController": "Address Name", "addressTextHintText": "Please enter the address name", "addressTextLabelText": "Address Name", "alertCancel": "After confirming the cancellation, you will continue to enjoy the benefits.", "alertMsgAdminVerify": "Your account has been created, Please login again.", "alertProceedToLogin": "Proceed to <PERSON>gin", "alertUnbind": "After confirming the unbinding, the full amount in the contract will be returned to your profit wallet after 30 working days, and you will continue to receive income until first 15 working days.", "alreadyHaveAccount": "Already have an account?  ", "amount": "Amount", "amountExceedsMaximumAllowed": "Amount exceeds maximum allowed", "amountMustBeInUnits": "Amount must be in units of", "amountMustBeMultipleOf": "Amount must be a multiple of", "anHourAgo": "an hour ago", "and": "& ", "appName": "SF India : Smart Crypto", "appUpdateAvailable": "App Update Available", "append": "Append", "appendRejectedAmount": "Append rejected amount", "approvedSuccess": "Your account has been approved", "atLeast8character": "At least 8 characters, must contain numbers, at least one upper case and lower case alphabet and must not contain spaces", "atLeast8characterWithoutUpperCase": "At least 8 characters, must contain numbers, one lower case alphabet and must not contain spaces", "authenticationFailed": "Authentication failed", "authenticationRequired": "Authentication required", "available": "Available", "availableBalance": "Available Balance", "availableBalanceIs": "Available balance is", "back": "Back", "balance": "Balance", "bankAccount": "Bank Account", "bankAccountNumber": "Bank Account Number", "bankName": "Bank Name", "bankTransfer": "Bank Transfer", "basicInformation": "Basic Information", "beneficiaryName": "Beneficiary Name", "biometricAuthenticationFailed": "Biometric authentication failed", "biometricAuthenticationNotAvailable": "Biometric authentication not available", "biometricAuthenticationNotSetup": "Biometric authentication not setup", "biometricAuthenticationRequired": "Biometric authentication required", "biometricAuthenticationSuccess": "Biometric authentication success", "biometricLogin": "Biometric Login", "biometricLoginDisabled": "Biometric login disabled", "biometricLoginEnabled": "Biometric login enabled", "biometricNotSupported": "Biometric not supported", "biometricPromptTitle": "Biometric Authentication", "biometricPromptSubtitle": "Use your biometric to authenticate", "biometricPromptDescription": "Place your finger on the sensor or look at the camera", "biometricPromptNegativeButton": "Cancel", "buy": "Buy", "buyNow": "Buy Now", "buyOrder": "Buy Order", "buyPrice": "Buy Price", "buySuccess": "Buy Success", "cancel": "Cancel", "cancelled": "Cancelled", "cannotBeEmpty": "Cannot be empty", "cannotExceed": "Cannot exceed", "cannotBeLessThan": "Cannot be less than", "changeLanguage": "Change Language", "changePassword": "Change Password", "changePasswordSuccess": "Password changed successfully", "checkYourInternetConnection": "Check your internet connection", "chooseLanguage": "Choose Language", "close": "Close", "completed": "Completed", "confirm": "Confirm", "confirmPassword": "Confirm Password", "confirmPasswordDoesNotMatch": "Confirm password does not match", "confirmPurchase": "Confirm Purchase", "confirmSale": "Confirm Sale", "confirmTransaction": "Confirm Transaction", "congratulations": "Congratulations", "contactSupport": "Contact Support", "continue": "Continue", "contractDetails": "Contract Details", "contractDuration": "Contract Duration", "contractExpired": "Contract Expired", "contractType": "Contract Type", "copy": "Copy", "copied": "<PERSON>pied", "copyAddress": "Copy Address", "copyLink": "Copy Link", "createAccount": "Create Account", "createNewAccount": "Create New Account", "createPassword": "Create Password", "currentBalance": "Current Balance", "currentPrice": "Current Price", "customerSupport": "Customer Support", "dailyProfit": "Daily Profit", "dashboard": "Dashboard", "date": "Date", "days": "Days", "delete": "Delete", "deleteAccount": "Delete Account", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "depositAddress": "Deposit Address", "depositAmount": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "depositHistory": "Deposit History", "depositSuccess": "Deposit Success", "details": "Details", "done": "Done", "download": "Download", "edit": "Edit", "editProfile": "Edit Profile", "email": "Email", "emailAddress": "Email Address", "emailVerification": "Email Verification", "emailVerified": "<PERSON><PERSON>", "enableBiometric": "Enable Biometric", "enableNotifications": "Enable Notifications", "enableTwoFactorAuthentication": "Enable Two Factor Authentication", "enter": "Enter", "enterAmount": "Enter Amount", "enterEmail": "<PERSON><PERSON>", "enterOTP": "Enter OTP", "enterPassword": "Enter Password", "enterPhoneNumber": "Enter Phone Number", "error": "Error", "errorOccurred": "An error occurred", "exit": "Exit", "expired": "Expired", "failed": "Failed", "firstName": "First Name", "forgotPassword": "Forgot Password", "fullName": "Full Name", "generateQR": "Generate QR", "help": "Help", "helpSupport": "Help & Support", "hide": "<PERSON>de", "history": "History", "home": "Home", "hours": "Hours", "ifsc": "IFSC", "insufficientBalance": "Insufficient Balance", "invalidAmount": "<PERSON><PERSON><PERSON>", "invalidEmail": "<PERSON><PERSON><PERSON>", "invalidOTP": "Invalid OTP", "invalidPassword": "Invalid Password", "invalidPhoneNumber": "Invalid Phone Number", "investment": "Investment", "investmentAmount": "Investment Amount", "investmentHistory": "Investment History", "investmentPlan": "Investment Plan", "investmentPlans": "Investment Plans", "investmentSuccess": "Investment Success", "investNow": "Invest Now", "language": "Choose Language", "lastName": "Last Name", "loading": "Loading", "login": "<PERSON><PERSON>", "loginSuccess": "Login Success", "logout": "Logout", "logoutConfirmation": "Are you sure you want to logout?", "market": "Market", "marketPrice": "Market Price", "maximum": "Maximum", "minimum": "Minimum", "minutes": "Minutes", "mobile": "Mobile", "mobileNumber": "Mobile Number", "name": "Name", "networkError": "Network Error", "newPassword": "New Wallet Password", "next": "Next", "no": "No", "noDataFound": "No Data Found", "noInternetConnection": "No Internet Connection", "notifications": "Notifications", "ok": "OK", "oldPassword": "Old Password", "otp": "E-mail verification code", "otpSent": "OTP Sent", "otpVerification": "OTP Verification", "otpVerified": "OTP Verified", "password": "Password", "passwordChanged": "Password Changed", "passwordDoesNotMatch": "Password does not match", "passwordResetSuccess": "Password reset successfully", "pending": "Pending", "phoneNumber": "Phone Number", "phoneVerification": "Phone Verification", "phoneVerified": "Phone Verified", "pleaseEnterValidAmount": "Please enter valid amount", "pleaseEnterValidEmail": "Please enter valid email", "pleaseEnterValidOTP": "Please enter valid OTP", "pleaseEnterValidPassword": "Please enter valid password", "pleaseEnterValidPhoneNumber": "Please enter valid phone number", "pleaseTryAgain": "Please try again", "pleaseWait": "Please wait!", "portfolio": "Portfolio", "price": "Price", "privacyPolicy": "Privacy Policy", "processing": "Processing", "profile": "Profile", "profit": "Profit", "profitHistory": "Profit History", "profitWallet": "Profit Wallet", "purchase": "Purchase", "purchaseHistory": "Purchase History", "purchaseSuccess": "Purchase Success", "qrCode": "QR Code", "quantity": "Quantity", "refresh": "Refresh", "register": "Register", "registrationSuccess": "Registration Success", "rejected": "Rejected", "resendOTP": "Resend OTP", "reset": "Reset", "resetPassword": "Reset Password", "retry": "Retry", "save": "Save", "search": "Search US stocks...", "seconds": "seconds", "security": "Security", "selectLanguage": "Select Language", "sell": "<PERSON>ll", "sellNow": "Sell Now", "sellOrder": "Sell Order", "sellPrice": "<PERSON><PERSON>", "sellSuccess": "<PERSON><PERSON>", "send": "Send", "sendOTP": "Send OTP", "settings": "Settings", "share": "Share", "show": "Show", "signIn": "Sign In", "signUp": "Sign Up", "skip": "<PERSON><PERSON>", "somethingWentWrong": "Something went wrong", "status": "Status", "submit": "Submit", "success": "Success", "successfullyCompleted": "Successfully Completed", "support": "Support", "termsAndConditions": "Terms & Conditions", "time": "Time", "today": "Today", "total": "Total", "totalAmount": "Total Amount", "totalBalance": "Total Balance", "totalInvestment": "Total Investment", "totalProfit": "Total Profit", "transaction": "Transaction", "transactionFailed": "Transaction Failed", "transactionHistory": "Transaction History", "transactionId": "Transaction ID", "transactionSuccess": "Transaction Success", "transfer": "Transfer", "transferAmount": "Transfer Amount", "transferSuccess": "Transfer Success", "tryAgain": "Try Again", "twoFactorAuthentication": "Two Factor Authentication", "type": "Type", "update": "Update", "updateAvailable": "Update Available", "updateProfile": "Update Profile", "updateSuccess": "Update Success", "upload": "Upload", "uploadDocument": "Upload Document", "uploadSuccess": "Upload Success", "username": "Username", "verification": "Verification", "verificationFailed": "Verification Failed", "verificationPending": "Verification Pending", "verificationSuccess": "Verification Success", "verify": "Verify", "verifyEmail": "<PERSON><PERSON><PERSON>", "verifyOTP": "Verify OTP", "verifyPhone": "Verify Phone", "version": "Version", "view": "View", "viewAll": "View All", "viewDetails": "View Details", "wallet": "wallet", "walletAddress": "Wallet Address", "walletBalance": "Wallet Balance", "warning": "Warning", "welcome": "Welcome!", "withdraw": "Withdraw", "withdrawAmount": "Withdraw Amount", "withdrawHistory": "Withdrawal History", "withdrawSuccess": "Withdraw Success", "withdrawal": "<PERSON><PERSON><PERSON>", "withdrawalAddress": "<PERSON><PERSON><PERSON> Address", "withdrawalFailed": "Withdrawal Failed", "withdrawalPending": "<PERSON><PERSON><PERSON>", "withdrawalSuccess": "<PERSON><PERSON>wal Success", "yes": "Yes", "yesterday": "Yesterday", "latestPrice": "Latest Price", "latestTransaction": "Latest\nTransaction\nPrice", "leadingConcept": "Leading Concept", "leadingIndustry": "Leading Industry", "letsStart": "Let's Start", "liveChat": "Live Chat", "liveChatDesc": "Chat with our support team", "lockPeriod": "Lock Period", "lockedBalance": "Locked Balance", "logIn": "Log In", "loginHintText": "<PERSON><PERSON> Username", "loginLabelText": "User Name", "loginLinkHintText": "http://supfut.com/login", "loginLinkLabelText": "Login Link", "loginLinkSuffix": "/login", "loginPasswordUpdatedToast": "Login Password Updated", "losers": "Losers", "lossesTimes": "Losses Times", "low": "Low", "mainstreamCurrency": "Mainstream\nCurrency", "maintenanceNotice": "Maintenance Notice", "marketOverview": "Market Overview", "marketUpdates": "Market Updates", "max": "Max", "maxDrawdown": "Max Drawdown", "maximumAmount": "Maximum amount is", "maximumPurchaseAmount": "Maximum Purchase Amount", "maximumWithdrawalAmount": "Maximum withdrawal amount is", "memberInfo": "Member Information", "members": "Members", "mentorCommission": "Mentor Commission", "mentors": "Mentors", "message": "Message", "minimumAmount": "Minimum amount is", "minimumAmountNotMeet": "Minimum amount not meet", "minimumPurchaseAmount": "Minimum Purchase Amount", "minimumWithdrawalAmount": "Minimum withdrawal amount is", "minutesAgo": "minutes ago", "missions": "<PERSON><PERSON>", "month": "Month", "monthly": "Monthly", "monthlyReturn": "Monthly Return", "mustBeMultipleOf": "must be a multiple of", "myContracts": "My Contracts", "nameValidatorMsg": "Must be more than 0 characters", "networkLineLabelText": "Network Line", "newPhoneNumberLabel": "New Phone Number", "newWalletPasswordLabel": "New Wallet Password", "news": "News", "newsUpdates": "News Updates", "noBalanceAvailable": "No balance available", "noBalanceInformation": "No balance information available", "noDataAvailable": "No data available", "noDetailsAvailable": "No details available", "noGalleryPermission": "Please enable photo permissions in settings to continue", "noInternet": "Please validate your network connection", "noPurchasesYet": "No purchases yet", "noResultsFound": "No results found", "notAvailable": "Not Available", "notification": "Notifications", "numOfContracts": "Number of Contracts", "numberOfTransactions": "Number of Transactions", "numberUpdatedToast": "Phone Number Updated", "numbering": "Numbering", "officeCompany": "Office Company", "onBoardScreenSubtitle": "Start investing today and your future will change for the better", "oneClickPurchase": "One Click Purchase", "oneClickSmartInvestmentDescription": "One-click smart investment is highly unified and easy to trade. Analysts will enter the market first in the institutional channel. No operation is required during the one-click smart investment period. Just follow the operation, freeing your hands to avoid forgetting to follow the purchase. Daily profits will be automatically restored.", "oneClickSmartInvestmentInstructions": "One-click Smart Investment Instructions", "oneMin": "1 minute", "open": "Open", "openWallet": "Open Wallet", "optional": "Optional", "or": "OR", "orderDate": "Order Date", "orderNo": "Order No", "otherMembers": "Other Members", "otpCodeError": "Enter exactly 6 digits", "otp_phone": "SMS verification code", "passport": "Passport", "passwordHint": "**********", "passwordHintText": "Enter Password", "passwordNotEqual": "Enter the matching password", "passwordUpdatedSuccessfully": "Password updated successfully", "passwordUpdatedToast": "Password Updated", "passwordValidationMsg": "Your password must be at least 8 characters long, contain at least one number and have a mixture of uppercase and lowercase letters.", "pastEarnings": "Past earnings", "paymentWallet": "Payment Wallet", "pending2": "Pending", "phone": "phone", "phoneSupport": "Phone Support", "phoneSupportDesc": "Call us", "phone_number": "Phone Number", "platformCommission": "Platform commission", "pleaseAcceptTheServiceAgreement": "Please accept the service agreement", "pleaseComplete": "Please complete the recharge \n within the 30 minutes", "pleaseDepositSomeAmountToContinue": "Please deposit some amount to continue", "pleaseEnterAValidAmount": "Please enter a valid amount", "pleaseEnterAValidInvestmentAmount": "Please enter a valid investment amount", "pleaseSelectAnInvestmentProduct": "Please select an investment product", "preview": "Preview", "proceedToLogin": "Proceed to <PERSON>gin", "productName": "Product Name", "products": "Products", "profitAmount": "Profit <PERSON>", "profitRatio": "Profit ratio", "profitTimes": "Profit Times", "progress": "Progress", "purchaseAmountBetween": "Purchase amount must be between {} and {}", "purchaseContracts": "Purchase Contracts", "purchaseDate": "Purchase date", "purchaseList": "Purchase List", "purchasePrice": "Purchasing price", "purchaseTime": "Purchase time", "purchased": "Purchased", "purchasedContracts": "Purchased Contracts", "purchasedProducts": "Purchased Products", "qrCodeScan": "Please scan the QR code", "readLess": "Less", "readMore": "More", "readyToUpdate": "Ready to Update", "realtime": "Realtime", "reason": "Reason", "rechargeAddress": "Recharge Address", "rechargeOrderSubmittedSuccessfully": "Recharge order submitted successfully", "rechargeQr": "Recharge QR code", "records": "Records", "referAndEarn": "Refer & Earn", "registerSuccess": "Your account has been created, Please login again.", "report": "Report a problem", "requestSuccess": "Request is successful", "resendCode": "Resend Code", "reservedPhone": "Reserved Phone", "retracementRate": "Retracement Rate", "retryUpdate": "Retry", "returnRate": "Return Rate", "revenue": "Revenue", "revenueDetails": "Revenue details", "reviewFailed": "Review failed, please submit again", "secondGeneration": "Second Generation", "secondsUpper": "Seconds", "securityOptionsLabel": "Security Options", "securitySettings": "Security Settings", "seeAll": "See All", "seeMore": "See More", "selectWithdrawalAddress": "Select Withdrawal Address", "sellDate": "Sell date", "sellQuantity": "Sell quantity", "sellTime": "<PERSON><PERSON>", "sellingPrice": "Selling price", "sellingTime": "Selling time", "sendCode": "Send Code", "sendCodeAlert": "The code has been sent to your account", "sendCodeToEmail": "Send code", "send_code": "Send Code", "serviceAgreement": "Service Agreement", "setAmount": "Set Amount", "setWalletPassword": "Set Wallet Password", "settled": "Settled", "shareText": "Register your referral with invitation link below", "signinAgree": "By signing up, I agree with the ", "signingYouOut": "Signing you out...", "singleAmount": "Single Amount", "size": "Size", "skipUpdate": "<PERSON><PERSON>", "smallRechargesBelow": "Small recharges below $100 will not be credited", "smartInvestment": "Smart Investment", "smartInvestmentCycle": "Smart Investment Cycle", "smartInvestmentProducts": "Smart Investment Products", "soldOut": "Sold Out", "somethingWentWrongTryAgain": "Something went wrong, Please try again", "starMentor": "Star Mentor", "startTime": "Start Time", "statistics": "Statistics", "stockCode": "Stock Code", "stockName": "Stock Name", "stockTrading": "Stock Trading", "stocks": "Stocks", "storagePermissionRequired": "Storage Permission Required", "subject": "Subject", "submitRecharge": "Submit Recharge", "submitRequest": "Submit Request", "successful": "Successful", "successfully": "Successfully", "suffixAddText": "Add +", "summary": "Summary", "summaryTransfer": "Are you sure to transfer the following amount?", "summaryWithdraw": "Are you sure to withdraw the following amount?", "systemNotifications": "System Notifications", "tasks": "Tasks", "tc": "T&C ", "theStatisticalSample": "The statistical sample is the data based on $10000 in the past 30 days.", "thirdGeneration": "Third Generation", "thirtyMin": "30 minutes", "thisRechargeAddressOneTime": "This recharge address is a one-time address", "timesUpper": "Times", "toCollectionWallet": " to Collection Wallet", "toCommunity": "To Community", "toCommunityWallet": " to Community Wallet", "toDepositWallet": " to Depo<PERSON>t <PERSON>", "toProfitWallet": " to Profit <PERSON><PERSON>", "todayProfit": "Today's profit", "todaysChange": "Today's\nChange", "todaysStockMarket": "Today's Stock Market", "toolTipWarning2": "The contract has reached the maximum operating scale and cannot be purchased temporarily", "tootTipWarning": "There are always risks\ninvolved with investing so\nit's important to do your\nown research and\nunderstand the potential\ndownsides.", "totalMembers": "Total Members", "totalRevenue": "Total revenue", "tradingDays": "Trading Days", "tradingWallet": "Trading Wallet", "transactionCycle": "Transaction Cycle", "transactionHash": "Transaction Hash", "transactionHashDescription": "🛡️ Used to track and verify transaction status on the blockchain", "transactionHashDescription2": "Think of it as a digital receipt for your transaction.", "transactionRecords": "Transaction Records", "transactionsHistory": "Transactions History", "transferChargedAmount": "The remaining {} will incur a 20% fee ({} in total)", "transferExpectedReceive": "The expected amount to receive is {}", "transferFreeAmount": "You can transfer {} for free", "transferTo": "Transfer ", "transferred": "Transferred", "trc20": "TRC 20", "trc20_description": "• Always 64 characters (hex format)\n• No 0x at the beginning (e.g., 59d24d4463a49e993dfe456317fe1b9e62e4b7817a9f44702c4c2139aabfcd2e)", "trc20_title": "✅ TRC20 (TRON network):", "tutorCommission": "Tutor Commission", "typeBack": "back", "typeCollection": "collection", "typeConfirmWallet": "confirmWallet", "typeDeposit": "deposit", "typeFront": "front", "typeGoogle": "google", "typePaymentERC": "ERC20", "typePaymentTRC": "TRC20", "unBindSuccess": "Unbinded successfully", "unbind": "Unbind", "unknown": "Unknown", "unknownVersion": "Unknown", "up": "Up", "upcoming": "Upcoming", "updateNow": "Update Now", "updateRequired": "Update Required", "updating": "Updating...", "uploadBackIdCard": "Upload back of ID/Passport/DL", "uploadFrontIdCard": "Upload front of ID/Passport/DL", "uploadIdBack": "Upload the ID/Passport/DL back", "uploadIdCard": "Upload ID/Passport/DL", "uploadIdFront": "Upload the ID/Passport/DL front", "uploadImageError": "File size is too large", "urlPrefix": "https://", "usMarket": "US Market", "usdt": "USDT", "user": "User", "validAddressNameMsg": "Enter a valid address name", "vipLevelError": "You need to reach the {} level to purchase", "vipNotice": "VIP Level Insufficient", "vipNoticeDescription": "Minimum VIP Level {} required to follow this mentor", "volume": "Volume", "walletAddressExists": "The withdraw address already exists", "walletNameExists": "The address name already exists", "walletPass": "Wallet Pass", "walletPassword": "Wallet Password", "walletPasswordShouldBe": "Wallet Password should be 6 characters", "walletUpdatedToast": "Wallet Password Updated", "weHaveSent": "We have sent the one-time password to your registered email", "week": "Week", "whatIsATransactionHash": "What is a Transaction Hash (TxHash)?", "whatIsATransactionHashDescription": "A transaction hash is a unique ID assigned to every transaction on a blockchain. It looks like a long string of letters and numbers (e.g., 0x59d24d44... or 59d24d44...).", "whatsappSupport": "WhatsApp Support", "whatsappSupportDesc": "Contact us via WhatsApp", "willBeSentToProfitWallet": "Will be sent to profit wallet", "winRate": "Win Rate", "withdrawAddressHintText": "Please enter the withdrawal address", "withdrawAddressLabelText": "<PERSON><PERSON><PERSON> Address", "withdrawAddresses": "Withdraw Addresses", "withdrawInvestment": "Withdraw Investment", "withdrawLimit": "You cannot withdraw below $100", "withdrawalAmount": "Withdrawal amount", "withdrawalFee": "<PERSON><PERSON><PERSON> Fee :", "withdrawnProfit": "Withdrawn profit", "workingAge": "Working Age", "years": "years", "yearsOfExperience": "Years of Experience", "zeroContractToast": "No contracts available", "unavailableFunds": "Unavailable funds", "availableFunds": "Available funds"}